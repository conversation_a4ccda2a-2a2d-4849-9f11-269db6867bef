const AWS = require("aws-sdk");
import { limTemplate } from "@functions/notification-service/emailtemplates/lim";
import { ibatTemplate } from "@functions/notification-service/emailtemplates/ibat";
import { hzuTemplate } from "@functions/notification-service/emailtemplates/hzu";
import { ucwTemplate } from "@functions/notification-service/emailtemplates/ucw";
import { getS3PdfObject } from "./getS3PdfObject";
import { unfcTemplate } from "./emailtemplates/unfc";
import { lsbfmyrTemplate } from "./emailtemplates/lsbfmyr";
import { uegTemplate } from "./emailtemplates/ueg";

AWS.config.update({ region: process.env.REGION });

export const sendEmail: any = async (event) => {
  console.log(event);
  const snsMessage = JSON.parse(event.Records[0].Sns.Message);
  if (snsMessage) {
    console.log(snsMessage);
    const {
      emailAddress,
      subject,
      firstName,
      lastName,
      brand,
      message,
      applicationId,
      businessUnitFilter,
      documentId,
      intakeDate,
      programmeName,
      additionalAttachments,
    } = snsMessage;
    let applicationPdfBase64;

    const name = `${documentId}.pdf`;
    const type = "Application form";
    const objectKey = `${applicationId}/${type}/${name}`;
    let bucketName;
    switch (brand || businessUnitFilter) {
      case "UEG":
        bucketName = process.env.CAMPUSNET_BUCKET_NAME;
        break;
      default:
        bucketName = process.env.REVIEW_CENTER_BUCKET_NAME;
    }

    try {
      const data = await getS3PdfObject(
        bucketName,
        objectKey,
        process.env.S3_BUCKET_ACCESS_ROLE_ARN
      );
      applicationPdfBase64 = data.toString("base64");
    } catch (err) {
      console.log("Function call Error[getS3PdfObject]" + err);
      throw err;
    }

    // Fetch additional attachments if present
    const additionalAttachmentsData = [];
    if (additionalAttachments && additionalAttachments.length > 0) {
      for (const attachment of additionalAttachments) {
        try {
          const data = await getS3PdfObject(
            bucketName,
            attachment.objectKey,
            process.env.S3_BUCKET_ACCESS_ROLE_ARN
          );
          additionalAttachmentsData.push({
            base64Data: data.toString("base64"),
            documentId: attachment.documentId
          });
        } catch (err) {
          console.log(`Error fetching additional attachment ${attachment.documentId}: ${err}`);
          throw err;
        }
      }
    }

    let htmlEmailTemplate = "";
    let senderEmail;
    switch (brand) {
      case "LIM College":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = limTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Programme Name}}",
          programmeName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Intake Date}}",
          intakeDate
        );
        break;
      case "LSBFMYR":
        senderEmail = process.env.LSBFMYR_SES_SENDER_EMAIL;
        htmlEmailTemplate = lsbfmyrTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Programme Name}}",
          programmeName
        );
        break;
      case "IBAT":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = ibatTemplate;
        htmlEmailTemplate = ibatTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        break;
      case "HZU":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = hzuTemplate;
        htmlEmailTemplate = hzuTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        break;
      case "UCW":
        senderEmail = process.env.UCW_SES_SENDER_EMAIL;
        htmlEmailTemplate = ucwTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.LastName}}",
          lastName
        );
        break;
      case "UEG":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = uegTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        break;
      case "UNFC":
        senderEmail = process.env.SES_SENDER_EMAIL;
        htmlEmailTemplate = unfcTemplate;
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.FirstName}}",
          firstName
        );
        htmlEmailTemplate = htmlEmailTemplate.replace(
          "{{Recipient.LastName}}",
          lastName
        );
        break;
    }

    const ses_mail = `From: ${senderEmail}
To: ${emailAddress}
Subject: ${subject}
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="NextPart"

--NextPart
Content-Type: text/html; charset=utf-8

${htmlEmailTemplate}

--NextPart
Content-Type: application/pdf; name=${applicationId}.pdf
Content-Disposition: attachment; filename=${applicationId}.pdf
Content-Transfer-Encoding: base64

${applicationPdfBase64}
${additionalAttachmentsData.map(attachment => `
--NextPart
Content-Type: application/pdf; name=${attachment.documentId}.pdf
Content-Disposition: attachment; filename=${attachment.documentId}.pdf
Content-Transfer-Encoding: base64

${attachment.base64Data}`).join('')}
--NextPart--`;

    const params = {
      RawMessage: {
        Data: ses_mail,
      },
    };

    try {
      const ses = new AWS.SES({ apiVersion: "2010-12-01" });
      await ses.sendRawEmail(params).promise();
    } catch (err) {
      console.log("[SERVER ERROR] Error in send email: " + err);
      return {
        message: "Error sending email",
      };
    }
  }

  return { message: "Email sent successfully" };
};