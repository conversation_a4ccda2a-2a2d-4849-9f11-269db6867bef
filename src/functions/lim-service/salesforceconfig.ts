export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  educationHistory: {
    institution: "InstitutionName__c",
    "city,regionDisplayName,countryDisplayName": "Address__c",
  },
  workHistory: {
    organizationName: "Employer__c",
    "city,stateDisplayName,countryDisplayName,phone.numberWithCode":
      "EmployerAddress__c",
    startDate: "StartDate__c",
    endDate: "EndDate__c",
    industryDisplayName: "Other_Industry__c",
    description: "NatureOfDuties__c",
    reasonForLeaving: "Reason_for_Leaving__c",
    startingPosition: "Position__c",
  },
  connections: {
    firstName: "First_Name_c__c",
    lastName: "Last_Name__c",
    organization: "Company__c",
    position: "Job_position__c",
    email: "Email_c__c",
    "telephone.numberWithCode": "Phone_c__c",
  },
  languageProficiency: {
    testTypeDisplayName: "ProficiencyQualification__c",
    testDate: "TestDate__c",
    testScore: "TestScore__c",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityExpiryDate: "Expiry_Date__c",
    identityType: "Identity_Type__c",
    identityIssuingCountry: "Issuing_Country__c",
    identityIssueDate: "Issue_Date__c",
  },
};
export const salesforceAgentConfig = {
  Survey_AP__c: {
    countryDisplayName: "Country__c",
    email: "Email_Address__c",
    proficiencyTestScore: "English_Proficiency_Test_Scores__c",
    backlogInDegree: "Have_20_Backlogs_in__c",
    overallCGPA: "Have_CGPA_at_2_0__c",
    overStayDuringVisa: "Have_you_overstayed__c",
    fundingForStudies: "How_will_you_be_funding_your_studies__c",
    businessUnitFilter: "Institution__c",
    fullName: "Name__c",
    "mobilePhone.numberWithCode": "Phone__c",
    isStudentVisaRejected: "Visa_Rejection_Last_6_Months__c",
    surveyIsQualified: "Qualified__c",
    isStudentVisaRejected_isQualified: "Q1_Qualification__c",
    backlogInDegree_isQualified: "Q2_Qualification__c",
    overallCGPA_isQualified: "Q3_Qualification__c",
    proficiencyTestScore_isQualified: "Q4_Qualification__c",
    fundingForStudies_isQualified: "Q5_Qualification__c",
    overStayDuringVisa_isQualified: "Q6_Qualification__c",
    surveyRecordTypeId: "RecordTypeId",
  },
  EducationHistoryRecord__c: ["educationHistory"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  IdentityInfoRecord__c: ["identityDocumentObject"],
  OpportunityFile__c: ["documents"],
  OpportunityTeamMember: ["teamMembers"],
  Connection__c: ["connections"],
  WorkHistoryRecord__c: ["workHistory"],
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "PersonEmail",
    countryDisplayName: ["Country__c", "CountryOfBirth__c"],
    "mobilePhone.numberWithCode": ["Mobile__c", "Phone"],
    city: "ShippingCity",
    mailingStateDisplayName: "PersonMailingState",
    mailingPostCode: "PersonMailingPostalCode",
    mailingStreet: "PersonMailingStreet",
    mailingCountryDisplayName: "PersonMailingCountry",
    mailingCity: "PersonMailingCity",
    mailingCountry: "PersonMailingCountryCode",
    DOB: "DateofBirth__c",
    accountRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    brand: "Brand__c",
    nationalityDisplayName: "Nationality__c",
    levelDisplayName: "Level__pc",
    preferredFirstName: "PreferedFirstName__c",
    otherLastNames: "Former_Last_Name__c",
    postCode: "ShippingPostalCode",
    genderDisplayName: "Gender__c",
    pronouns: "Preferred_Gender_Pronoun__c",
    primaryCitizenShipDisplayName: "Citizenship__c",
    dualCitizenShipDisplayName: "Dual_Citizenship__c",
    primaryLanguageDisplayName: "CommunicationLanguage__c",
    genderIdentity: "PersonGenderIdentity",
    agentContactUserId: "OwnerId",
    privacyPolicyConsent:["HasOptedOutOfMarketingEmail__c", "HasOptedOutOfGUSMarketingEmail__c"]
  },
  Lead: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "Email",
    countryDisplayName: "Country",
    "mobilePhone.numberWithCode": ["Phone", "MobilePhone"],
    nationalityDisplayName: "Nationality__c",
    leadSource: "LeadSource",
    brand: "Brand__c",
    street: "Street",
    postCode: "PostalCode",
    DOB: "DateofBirth__c",
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    program: "Programme__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    genderDisplayName: "Gender__c",
    agentContactUserId: "OwnerId",
    privacyPolicyConsent:["HasOptedOutOfMarketingEmailSync__c", "HasOptedOutOfGUSMarketingEmail__c"]
  },
  Individual: {
    email: "Email__c",
    brand: "Brand__c",
    DOB: "BirthDate",
    lastName: "LastName",
    firstName: "FirstName",
  },
  Application__c: {
    "homePhone.numberWithCode": "Tel_home__c",
    accountManagerUserId: "Business_Developer__c",
    agentContactUserId: "OwnerId",
    firstName: "First_Name__c",
    fullName: "Name",
    lastName: "Last_Name__c",
    email: "Email__c",
    "mobilePhone.numberWithCode": "Mobile__c",
    levelDisplayName: "Level__c",
    programDisplayName: "Program_Of_Study__c",
    intake: "Intake__c",
    street: "Street_Address__c",
    city: "City__c",
    state: "State_Province__c",
    postCode: "Postcode__c",
    countryDisplayName: ["Country__c", "Country_of_Birth__c"],
    nationalityDisplayName: "Nationality__c",
    DOB: "Date_of_birth__c",
    passportExpiryDate: "Passport_Date_of_expiry__c",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",
  },
  Opportunity: {
    appId: "ApplicationId__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    agentContactId: "Agent_Contact__c",
    agentAccountId: "AgentAccount__c",
    email: "Email__c",
    city: "gaconnector_City__c",
    state: "State__c",
    program: "Programme__c",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    intake: ["CloseDate", "Product_Intake_Date__c"],
    // opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    pricebookId: "Pricebook2Id",
    "mobilePhone.numberWithCode": ["AccountPhone__c", "AccountMobile__c"],
    acceptedHonestyStatement: "DeclarationInfoProvided__c",
    legalNotices: "Legal_Notice__c",
    acceptedPrivacyPolicy: "DeclarationPrivacyPolicy__c",
    submittedDate: "DeclarationDate__c",
    submissionSignature: "DeclarationSignature__c",
    admissionStage: "AdmissionsStage__c",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
};

export const salesforceStudentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
  },
  educationHistory: {
    institution: "InstitutionName__c",
    "city,regionDisplayName,countryDisplayName": "Address__c",
  },
  workHistory: {
    organizationName: "Employer__c",
    "city,stateDisplayName,countryDisplayName,phone.numberWithCode":
      "EmployerAddress__c",
    startDate: "StartDate__c",
    endDate: "EndDate__c",
    industryDisplayName: "Other_Industry__c",
    description: "NatureOfDuties__c",
    reasonForLeaving: "Reason_for_Leaving__c",
    startingPosition: "Position__c",
  },
  connections: {
    firstName: "First_Name_c__c",
    lastName: "Last_Name__c",
    organization: "Company__c",
    position: "Job_position__c",
    email: "Email_c__c",
    "telephone.numberWithCode": "Phone_c__c",
  },
  languageProficiency: {
    testTypeDisplayName: "ProficiencyQualification__c",
    testDate: "TestDate__c",
    testScore: "TestScore__c",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityExpiryDate: "Expiry_Date__c",
    identityType: "Identity_Type__c",
    identityIssuingCountry: "Issuing_Country__c",
    identityIssueDate: "Issue_Date__c",
  },
};
export const salesforceStudentConfig = {
  Survey_AP__c: {
    countryDisplayName: "Country__c",
    email: "Email_Address__c",
    proficiencyTestScore: "English_Proficiency_Test_Scores__c",
    backlogInDegree: "Have_20_Backlogs_in__c",
    overallCGPA: "Have_CGPA_at_2_0__c",
    overStayDuringVisa: "Have_you_overstayed__c",
    fundingForStudies: "How_will_you_be_funding_your_studies__c",
    businessUnitFilter: "Institution__c",
    fullName: "Name__c",
    phoneNumber: "Phone__c",
    isStudentVisaRejected: "Visa_Rejection_Last_6_Months__c",
    surveyIsQualified: "Qualified__c",
    isStudentVisaRejected_isQualified: "Q1_Qualification__c",
    backlogInDegree_isQualified: "Q2_Qualification__c",
    overallCGPA_isQualified: "Q3_Qualification__c",
    proficiencyTestScore_isQualified: "Q4_Qualification__c",
    fundingForStudies_isQualified: "Q5_Qualification__c",
    overStayDuringVisa_isQualified: "Q6_Qualification__c",
    surveyRecordTypeId: "RecordTypeId",
  },
  EducationHistoryRecord__c: ["educationHistory"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  IdentityInfoRecord__c: ["identityDocumentObject"],
  OpportunityFile__c: ["documents"],
  OpportunityTeamMember: ["teamMembers"],
  Connection__c: ["connections"],
  WorkHistoryRecord__c: ["workHistory"],
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "PersonEmail",
    countryDisplayName: ["Country__c", "CountryOfBirth__c"],
    "mobilePhone.numberWithCode": ["Mobile__c", "Phone"],
    city: ["ShippingCity", "gaconnector_City__c"],
    mailingStateDisplayName: "PersonMailingState",
    mailingPostCode: "PersonMailingPostalCode",
    mailingStreet: "PersonMailingStreet",
    mailingCountryDisplayName: "PersonMailingCountry",
    mailingCity: "PersonMailingCity",
    mailingCountry: "PersonMailingCountryCode",
    DOB: "DateofBirth__c",
    accountRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    brand: "Brand__c",
    nationalityDisplayName: "Nationality__c",
    levelDisplayName: "Level__pc",
    preferredFirstName: "PreferedFirstName__c",
    otherLastNames: "Former_Last_Name__c",
    postCode: "ShippingPostalCode",
    genderDisplayName: "Gender__c",
    pronouns: "Preferred_Gender_Pronoun__c",
    primaryCitizenShipDisplayName: "Citizenship__c",
    dualCitizenShipDisplayName: "Dual_Citizenship__c",
    primaryLanguageDisplayName: "CommunicationLanguage__c",
    genderIdentity: "PersonGenderIdentity",
    privacyPolicyConsent:["HasOptedOutOfMarketingEmail__c", "HasOptedOutOfGUSMarketingEmail__c"]
  },
  Lead: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "Email",
    countryDisplayName: "Country",
    "mobilePhone.numberWithCode": ["MobilePhone", "Phone"],
    opportunityApplicationSource: "ApplicationSource__c",
    nationalityDisplayName: "Nationality__c",
    leadSource: "LeadSource",
    brand: "Brand__c",
    street: "Street",
    postCode: "PostalCode",
    DOB: "DateofBirth__c",
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    program: "Programme__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    genderDisplayName: "Gender__c",
    privacyPolicyConsent:["HasOptedOutOfMarketingEmailSync__c", "HasOptedOutOfGUSMarketingEmail__c"]
  },
  Individual: {
    email: "Email__c",
    brand: "Brand__c",
    DOB: "BirthDate",
    lastName: "LastName",
    firstName: "FirstName",
  },
  Opportunity: {
    appId: "ApplicationId__c",
    email: "Email__c",
    city: "gaconnector_City__c",
    state: "State__c",
    program: "Programme__c",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    intake: ["CloseDate", "Product_Intake_Date__c"],
    // opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    pricebookId: "Pricebook2Id",
    "mobilePhone.numberWithCode": ["AccountPhone__c", "AccountMobile__c"],
    acceptedHonestyStatement: "DeclarationInfoProvided__c",
    legalNotices: "Legal_Notice__c",
    acceptedPrivacyPolicy: "DeclarationPrivacyPolicy__c",
    submittedDate: "DeclarationDate__c",
    submissionSignature: "DeclarationSignature__c",
    admissionStage: "AdmissionsStage__c",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
};
