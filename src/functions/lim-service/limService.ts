import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
  salesforceStudentConfig,
  salesforceStudentSubObjectsConfig,
} from "src/functions/lim-service/salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });

const sqs = new AWS.SQS();

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);

export const handleLimSfSaveOrUpdateRequests = async (event) => {
  console.log("event -->", JSON.stringify(event));
  const loggerEnum = new LoggerEnum();
  const brand = "LIM College";
  for (let record of event) {
    let applicationDetails = JSON.parse(record["body"]);
    const filledBy = applicationDetails?.applicationFilledBy;
    if (applicationDetails?.privacyPolicyConsent) {
      applicationDetails.privacyPolicyConsent =
        !applicationDetails?.privacyPolicyConsent;
    }
    let referenceMappedDetails;
    if (applicationDetails?.levelDisplayName === "Undergraduate") {
      const mappings = {
        highSchoolInstitution: "institution",
        highSchoolCountry: "country",
        highSchoolRegionDisplayName: "regionDisplayName",
        highSchoolCity: "city",
        highSchoolEndDate: "endDate",
        highSchoolLevelOfStudy: "levelOfStudy",
        highSchoolAcademicCertificate: "academicCertificate",
        highSchoolRegion: "region",
        highSchoolStartDate: "startDate",
        highSchoolCountryDisplayName: "countryDisplayName",
      };
      let ugInstitution: any = {};
      for (const [sourceKey, targetKey] of Object.entries(mappings)) {
        if (applicationDetails.hasOwnProperty(sourceKey)) {
          if (sourceKey === "highSchoolAcademicCertificatee") {
            ugInstitution[targetKey] = [].push(
              applicationDetails[sourceKey[0]]
            );
          } else {
            ugInstitution[targetKey] = applicationDetails[sourceKey];
          }
        }
      }
      if (!applicationDetails.hasOwnProperty("institutions")) {
        applicationDetails["institutions"] = [];
      }
      applicationDetails["institutions"].push(ugInstitution);
    }
    if (applicationDetails?.applicationStatus === "submitted") {
      applicationDetails.identityDocumentObject = [
        {
          identityNumber: applicationDetails.passportNumber,
          identityExpiryDate: applicationDetails.passportExpiryDate,
          identityIssuingCountry:
            applicationDetails.passportIssuingCountryDisplayName,
          identityType: "Passport",
          identityIssueDate: applicationDetails.passportIssueDate,
        },
      ];
      const mappings = {
        institutions: "educationHistory",
        testInfo: "languageProficiency",
        recommenders: "connections",
        employmentHistory: "workHistory",
      };
      for (const [sourceKey, targetKey] of Object.entries(mappings)) {
        if (applicationDetails.hasOwnProperty(sourceKey)) {
          if (sourceKey === "employmentHistory") {
            applicationDetails[sourceKey].forEach((job) => {
              if (job.startDate) {
                // Convert MM-YYYY to YYYY-MM-01
                const [month, year] = job?.startDate?.split("-");
                job.startDate = `${year}-${month}-01`;
              }
              if (job.endDate) {
                // Convert MM-YYYY to YYYY-MM-01
                const [month, year] = job?.endDate?.split("-");
                job.endDate = `${year}-${month}-01`;
              }
            });
          }
          applicationDetails[targetKey] = applicationDetails[sourceKey];
        }
      }
      referenceMappedDetails = await opportunityFileReferenceMapping(
        applicationDetails
      );
      applicationDetails = referenceMappedDetails.appDetails;
    }
    const requestId = applicationDetails.requestId;
    const APIKEY = applicationDetails.APIKEY;
    const currentUTC = new Date().toISOString();

    let request = {};
    try {
      request["sectionLabel"] = applicationDetails.sectionLabel;
      request["email"] = applicationDetails.email;
      request["applicationId"] = applicationDetails.applicationId;
      request["requestId"] = requestId;
      request["messageId"] = record.messageId;
      if (
        referenceMappedDetails?.institutionData ||
        referenceMappedDetails?.testData
      ) {
        console.log('has ref')
        const mappedData = await Promise.all([
          referenceMappedDetails?.institutionData
            ? mapSalesforceObject(
                referenceMappedDetails.institutionData,
                filledBy
              )
            : null,
          referenceMappedDetails?.testData
            ? mapSalesforceObject(referenceMappedDetails.testData, filledBy)
            : null,
          mapSalesforceObject(applicationDetails, filledBy),
        ]);

        const [mappedInstitutionData, mappedTestData, mappedRequest] =
          mappedData;
        await Promise.all([
          mappedInstitutionData &&
            postDataSf(
              { ...request, ...mappedInstitutionData },
              APIKEY,
              filledBy
            ),
          mappedTestData &&
            postDataSf({ ...request, ...mappedTestData }, APIKEY, filledBy),
          postDataSf({ ...request, ...mappedRequest }, APIKEY, filledBy),
        ]);
      } else {
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };
        console.log("request ->", request);
        await postDataSf(request, APIKEY, filledBy);
      }
      await sqs.deleteMessage({
        QueueUrl: process.env.LIM_EIP_OUEUE_URL,
        ReceiptHandle: record.receiptHandle,
      });

      await cloudWatchLoggerService.log(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        "Successfully saved to salesforce",
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    } catch (error) {
      await cloudWatchLoggerService.error(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SAVED_TO_SALESFORCE,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        error.errorDetails
          ? typeof error.errorDetails === "string"
            ? error.errorDetails
            : JSON.stringify(error.errorDetails)
          : JSON.stringify(error),
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    }
  }
};
async function mapSalesforceObject(applicationDetails, filledBy): Promise<any> {
  try {
    let salesforceConfig =
      filledBy === "agent"
        ? { ...salesforceAgentConfig }
        : { ...salesforceStudentConfig };
    if (applicationDetails.hasOwnProperty("notMetEligibilityCreteria")) {
      if (applicationDetails.notMetEligibilityCreteria === true) {
        const keys = Object.keys(salesforceConfig);
        for (const key of keys) {
          if (!["Survey_AP__c"].includes(key)) {
            delete salesforceConfig[key];
          }
        }
      }
      console.log("Survey Object Created Successfully", salesforceConfig);

      applicationDetails.surveyIsQualified =
        !applicationDetails.notMetEligibilityCreteria;
    } else {
      delete salesforceConfig["Survey_AP__c"];
    }
    const salesforceSubObjectsConfig =
      filledBy === "agent"
        ? salesforceAgentSubObjectsConfig
        : salesforceStudentSubObjectsConfig;

    let result = {};
    for (const object in salesforceConfig) {
      if (Array.isArray(salesforceConfig[object])) {
        if (applicationDetails[salesforceConfig[object][0]]) {
          for (const record of applicationDetails[
            salesforceConfig[object][0]
          ]) {
            let mappedObject = await mapValues(
              record,
              salesforceSubObjectsConfig[salesforceConfig[object][0]]
            );
            if (mappedObject && Object.keys(mappedObject).length > 0) {
              if (!result[object]) {
                result[object] = [];
              }
              result[object].push(mappedObject);
            }
          }
        }
      } else {
        let mappedObject = await mapValues(
          applicationDetails,
          salesforceConfig[object]
        );
        console.log(mappedObject);
        if (mappedObject && Object.keys(mappedObject).length > 0) {
          result[object] = mappedObject;
        }
      }
    }
    return result;
  } catch (error) {
    throw error;
  }
}

function opportunityFileReferenceMapping(appDetails) {
  if (appDetails && Array.isArray(appDetails.documents)) {
    const documentMap = new Map();
    appDetails.documents.forEach((doc) => {
      documentMap.set(doc.documentId, doc);
    });

    const addReferences = (documentIds, referenceKey, referenceValue) => {
      documentIds.forEach((docId) => {
        const matchingDocument = documentMap.get(docId);
        if (matchingDocument) {
          matchingDocument[referenceKey] = referenceValue;
        }
      });
    };
    let institutionData = {
      documents: [],
      educationHistory: [],
    };

    let testData = {
      documents: [],
      languageProficiency: [],
    };

    if (Array.isArray(appDetails.educationHistory)) {
      appDetails.educationHistory.forEach((institution, index) => {
        const institutionOrder = index + 1;
        const academicCertificate = institution.academicCertificate || [];

        const documentIds = [
          ...academicCertificate.map((doc) => doc.documentId),
        ];

        addReferences(
          documentIds,
          "eduReference",
          `@{EducationHistoryRecord__c_${institutionOrder}.id}`
        );

        institutionData.documents.push(
          ...documentIds.map((docId) => documentMap.get(docId)).filter(Boolean)
        );
        institutionData.educationHistory.push(institution);
      });

      appDetails.educationHistory = [];
      appDetails.documents = appDetails.documents.filter(
        (doc) =>
          !institutionData.documents.some(
            (institutionDoc) => institutionDoc.documentId === doc.documentId
          )
      );
    } else {
      console.error("No institutions found in appDetails. Nothing to process.");
    }

    if (Array.isArray(appDetails.languageProficiency)) {
      appDetails.languageProficiency.forEach((test, index) => {
        const testOrder = index + 1;
        const testCertificateId = test.testCertificateId || [];

        const documentIds = [...testCertificateId.map((doc) => doc.documentId)];

        addReferences(
          documentIds,
          "testReference",
          `@{LanguageProficiencyRecord__c_${testOrder}.id}`
        );

        testData.documents.push(
          ...documentIds.map((docId) => documentMap.get(docId)).filter(Boolean)
        );
        testData.languageProficiency.push(test);
      });

      appDetails.languageProficiency = [];
      appDetails.documents = appDetails.documents.filter(
        (doc) =>
          !testData.documents.some(
            (testDoc) => testDoc.documentId === doc.documentId
          )
      );
    } else {
      console.error("No test data found in appDetails. Nothing to process.");
    }

    const categories = [
      {
        key: "passport",
        referenceKey: "identityReference",
        referenceValue: "@{IdentityInfoRecord__c_1.id}",
      },
    ];

    categories.forEach(({ key, referenceKey, referenceValue }) => {
      if (Array.isArray(appDetails[key])) {
        const documentIds = appDetails[key].map((doc) => doc.documentId);
        addReferences(documentIds, referenceKey, referenceValue);
      }
    });

    console.log("Processed appDetails.documents:", appDetails.documents);
    console.log("Institution data moved:", institutionData);
    console.log("TestData moved:", testData);

    return { appDetails, institutionData, testData };
  } else {
    console.error("Invalid or missing appDetails.documents.");
  }

  return {
    appDetails,
    institutionData: { documents: [], educationHistory: [] },
    testData: { documents: [], languageProficiency: [] },
  };
}

async function mapValues(source, mapping) {
  const result = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value;

    if (sourceKey.includes(",")) {
      const keys = sourceKey.split(",");
      const values = [];

      for (const key of keys) {
        if (key.includes(".")) {
          const nestedKeys = key.trim().split(".");
          let nestedValue = source;

          for (const nestedKey of nestedKeys) {
            nestedValue = nestedValue ? nestedValue[nestedKey] : undefined;
          }
          if (nestedValue !== undefined) {
            values.push(nestedValue);
          }
        } else {
          const simpleValue = source[key.trim()];
          if (simpleValue !== undefined) {
            values.push(simpleValue);
          }
        }
      }

      value = values.join(" ");
    } else if (sourceKey.includes(".")) {
      const keys = sourceKey.split(".");
      value = source;

      for (const key of keys) {
        value = value ? value[key] : undefined;
      }
    } else {
      value = source[sourceKey];
    }

    if (value !== undefined) {
      if (Array.isArray(targetKey)) {
        targetKey.forEach((key) => {
          result[key] = value;
        });
      } else {
        result[targetKey] = value;
      }
    }
  }

  return result;
}
