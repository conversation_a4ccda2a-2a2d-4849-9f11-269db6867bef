import { DynamoDBService } from "src/common/dynamodbService";
import { SNS } from "aws-sdk";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";

const dbService = new DynamoDBService();
const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();
let correlationId: string;
let brandName: string;
let email: string;
const oapNameMapping = {
  "LIM College": "LIM",
};
export const studentLeadOwnerService = async (event) => {
  for (let record of event) {
    const platformEventMessage = JSON.parse(record.body);
    console.log("platformEventMessage", JSON.stringify(platformEventMessage));
    brandName = platformEventMessage.payload.BusinessUnitFilter__c;
    email = platformEventMessage.payload.Email__c;
    correlationId = platformEventMessage.event?.EventUuid;
    await log(
      platformEventMessage.message,
      `Student application reprocess initiated: ${email}`,
      "STUDENT_LEAD_ASSIGNMENT",
      email
    );
    try {
      let applications = await getApplicationsByEmail();
      if (applications.length > 0) {
        for (let application of applications) {
          if (
            !application.hasOwnProperty("hasActiveLeadOwner") ||
            application.hasActiveLeadOwner === false
          ) {
            application.hasActiveLeadOwner = true;
            application = await updateDocs(application);
            const messageAttributes = {
              source: {
                DataType: "String",
                StringValue: oapNameMapping[brandName] ?? brandName,
              },
            };
            const keys = JSON.parse(process.env.API_KEYS);
            if (application.applicationStatus === "submitted") {
              const draftApplicationDetails = await formDraftApplicationDetails(
                application
              );
              const snsMessageDraftApplication = { ...draftApplicationDetails };
              snsMessageDraftApplication["requestId"] = correlationId;
              snsMessageDraftApplication["APIKEY"] =
                keys[oapNameMapping[brandName] ?? brandName.toUpperCase()];
              await log(
                platformEventMessage.message,
                "post draft application message in sns initiated",
                "GUS_OAP_TOPIC",
                application.email,
                process.env.OAP_SF_TOPIC_ARN,
                JSON.stringify(snsMessageDraftApplication),
                null
              );
              let draftApplicationMessageDetails = await publishMessageToSNS(
                JSON.stringify(snsMessageDraftApplication),
                process.env.OAP_SF_TOPIC_ARN,
                messageAttributes,
                application.applicationId
              );
              await log(
                platformEventMessage.message,
                "post draft application message in sns completed",
                "GUS_OAP_TOPIC",
                application.email,
                process.env.OAP_SF_TOPIC_ARN,
                JSON.stringify(snsMessageDraftApplication),
                draftApplicationMessageDetails
              );
            }
            const snsMessage = { ...application };
            snsMessage["requestId"] = correlationId;
            snsMessage["APIKEY"] =
              keys[oapNameMapping[brandName] ?? brandName.toUpperCase()];
            await log(
              platformEventMessage.message,
              "post message in sns initiated",
              "GUS_OAP_TOPIC",
              application.email,
              process.env.OAP_SF_TOPIC_ARN,
              JSON.stringify(snsMessage),
              null
            );
            let messageDetails = await publishMessageToSNS(
              JSON.stringify(snsMessage),
              process.env.OAP_SF_TOPIC_ARN,
              messageAttributes,
              application.applicationId
            );
            await log(
              platformEventMessage.message,
              "post message in sns completed",
              "GUS_OAP_TOPIC",
              application.email,
              process.env.OAP_SF_TOPIC_ARN,
              JSON.stringify(snsMessage),
              messageDetails
            );
            const updateRequest = {
              hasActiveLeadOwner: true,
            }
            if (application.applicationStatus === "submitted") {
              updateRequest["submittedToSf"] = true;
            }
            await dbService.updateObject(
              `gus-oap-student-applications-${process.env.STAGE}`,
              { PK: application.PK, SK: application.SK },
              updateRequest
            );
          } else{
            await log(
              platformEventMessage.message,
              "application already processed with active lead owner",
              "APPLICATION_ALREADY_PROCESSED",
              application.email,
              '',
              application,
              null
            );
          }
        }
      } else {
        await log(
          platformEventMessage.message,
          `No application found for emai: ${email}`,
          "STUDENT_LEAD_ASSIGNMENT",
          email
        );
      }
    } catch (error) {
      await error(
        platformEventMessage.message,
        error,
        "STUDENT_LEAD_ASSIGNMENT_FAILED",
        email
      );
      throw error;
    }
  }
};

export const formDraftApplicationDetails = async (applicationDetail) => {
  const draftApplication = { ...applicationDetail };
  draftApplication["stage"] = "Application";
  draftApplication["admissionStage"] = "Draft Application";
  draftApplication["isSubmitted"] = false;
  draftApplication["applicationStatus"] = "inProgress";
  delete draftApplication.documents;
  return draftApplication;
};

export const publishMessageToSNS = async (
  message: string,
  topicArn: string,
  attributes: Record<string, any>,
  messageGroupId: string = null
) => {
  let sns: AWS.SNS;
  sns = new SNS({ region: process.env.REGION });
  const params = {
    Message: message,
    MessageAttributes: attributes,
    TopicArn: topicArn,
  };
  if (messageGroupId) {
    params["MessageGroupId"] = messageGroupId;
  }
  try {
    return sns.publish(params).promise();
  } catch (error) {
    await error(
      params,
      error,
      "STUDENT_LEAD_ASSIGNMENT_POST_SNS_FAILED",
      email
    );
    // throw error;
  }
};

export const getApplicationsByEmail = async () => {
  const params = {
    TableName: `gus-oap-student-applications-${process.env.STAGE}`,
    KeyConditionExpression:
      "PK = :pkValue AND begins_with(SK, :startsWithValue)",
    FilterExpression: "applicationFilledBy = :filledBy",
    ExpressionAttributeValues: {
      ":pkValue": email,
      ":startsWithValue": oapNameMapping[brandName] ?? brandName.toUpperCase(),
      ":filledBy": "student",
    },
  };
  try {
    const response = await dbService.queryObjects(params);
    if (response.Items && response.Items.length > 0) {
      await log(
        params,
        "get applications by email and brand",
        "GET_APPLICATIONS_BY_EMAIL",
        email,
        response.Items
      );
      return response.Items;
    } else {
      return [];
    }
  } catch (err) {
    await error(params, err, "GET_APPLICATION_BY_EMAIL_FAILED", email);
    throw new Error(`Error in get applications: ${err}`);
  }
};

export const updateDocs = async (application) => {
  if (
    application.applicationStatus === "submitted" &&
    !application?.submittedToSf
  ) {
    const params = {
      TableName: `gus-oap-student-application-documents-${process.env.STAGE}`,
      KeyConditionExpression: "PK = :pkValue and begins_with(SK, :skValue)",
      ExpressionAttributeValues: {
        ":pkValue": application.PK,
        ":skValue": `${application.SK}_`,
      },
    };
    try {
      const applicantDocDetails = await dbService.queryObjects(params);
      application["documents"] = applicantDocDetails?.Items;
      return application;
    } catch (error) {
      await error(
        params,
        error,
        "GET_APPLICATION_DOCUMENTS_FAILED",
        application.email
      );
      // throw new Error(`Error in get documents: ${error}`);
    }
  }
  return application;
};
export const log = async (
  sourcePayload,
  logMessage,
  event,
  secondaryKey?,
  destination?,
  destinationPayload?,
  destinationObject?,
  destinationObjectId?
) => {
  await cloudWatchLoggerService.log(
    correlationId,
    new Date().toISOString(),
    loggerEnum.Component.OAP_HANDLERS,
    loggerEnum.Component.GUS_SALESFORCE_EVENTS_LISTENER,
    "GUS_OAP_TOPIC",
    event,
    "STUDENT_OAP_LEAD_OWNER_ASSIGNMENT",
    sourcePayload,
    destinationPayload,
    logMessage,
    brandName,
    secondaryKey,
    `oap-handlers/${correlationId}/${secondaryKey}`,
    "Email",
    secondaryKey,
    destinationObject,
    destinationObjectId,
    destination
  );
};
export const error = async (
  sourcePayload,
  errorMessage,
  event,
  secondaryKey?,
  destination?,
  destinationPayload?,
  destinationObject?,
  destinationObjectId?
) => {
  await cloudWatchLoggerService.error(
    correlationId,
    new Date().toISOString(),
    loggerEnum.Component.OAP_HANDLERS,
    loggerEnum.Component.GUS_SALESFORCE_EVENTS_LISTENER,
    "GUS_OAP_TOPIC",
    event,
    "STUDENT_OAP_LEAD_OWNER_ASSIGNMENT",
    sourcePayload,
    destinationPayload,
    errorMessage,
    brandName,
    secondaryKey,
    `oap-handlers/${correlationId}/${secondaryKey}`,
    "Email",
    secondaryKey,
    destinationObject,
    destinationObjectId,
    destination
  );
};
