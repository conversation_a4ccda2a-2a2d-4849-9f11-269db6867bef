import {
  hzuForgotPasswordEmailTemplate,
  hzuWelcomeEmailTemplate,
  limForgotPasswordEmailTemplate,
  limWelcomeEmailTemplate,
  ucwForgotPasswordEmailTemplate,
  ucwWelcomeEmailTemplate,
  unfcForgotPasswordEmailTemplate,
  unfcWelcomeEmailTemplate,
  uegWelcomeEmailTemplate,
  uegForgotPasswordEmailTemplate,
  uegWelcomeEmailTemplateDe,
  uegForgotPasswordEmailTemplateDe
} from "@functions/cognito-trigger/email-templates/index";
import { CognitoIdentityServiceProvider } from "aws-sdk";
import { userPoolDomainMappings } from "./userPoolDomainMappings";
import { DynamoDB } from "aws-sdk";

const cognito = new CognitoIdentityServiceProvider();
const dynamoDB = new DynamoDB.DocumentClient();

const DELETE_AFTER_HOURS = 24; // Time after which to delete unconfirmed users

// Function to fetch student data from DynamoDB with initial delay
const getStudentData = async (email: string, oapName: string, delayMs: number = 2000) => {
  const params = {
      TableName: `gus-oap-student-details-${process.env.STAGE || 'dev'}`,
      Key: {
        PK: email,
        SK: oapName.toUpperCase()
      }
    };
    console.log("params:", params);

    try {
      // Add delay before fetching to allow frontend to update database
      console.log(`Waiting ${delayMs}ms before fetching student data for ${email}...`);
      await new Promise(resolve => setTimeout(resolve, delayMs));

      console.log(`Fetching student data for ${email}`);
      const result = await dynamoDB.get(params).promise();

      console.log(`Student data result:`, result.Item);
      return result.Item;

    } catch (error) {
      console.error(`Error fetching student data:`, error);
      return null;
    }
};

export const studentOAPCustomEmail = async (event, context) => {
  console.log(JSON.stringify(event));

  const domain = userPoolDomainMappings[event.userPoolId]?.domain;
  const oapName = userPoolDomainMappings[event.userPoolId]?.oapName;
  const userEmail = event.request.userAttributes.email;

  console.log('came localization');
  // Fetch student data to get localization for UEG brand only
  let localization = "en"; // Default to English
  if (oapName === "ueg") {
    console.log('came localization 2');
    const studentData = await getStudentData(userEmail, oapName);
    console.log('studentData:', studentData);
    if (studentData && studentData.localization) {
      console.log('came localization 3');
      localization = studentData.localization;
    } else {
      console.log('came localization 4');
    }
  }

  if (event.triggerSource === "CustomMessage_ForgotPassword") {
    const { userAttributes } = event.request;
    const firstName = userAttributes["custom:firstName"] || "";
    const resetLink = `${domain}/change-password?confirmation_code=${event.request.codeParameter}&user_name=${event.userName}`;
    let emailHtmlContent = getForgotPasswordEmailTemplate(oapName, localization);
    
    emailHtmlContent = emailHtmlContent
      .replace("{{Recipient.FirstName}}", firstName)
      .replaceAll("{{ResetLink}}", resetLink);
    event.response.emailMessage = emailHtmlContent;
    event.response.emailSubject = localization === "de" ? "Passwort zurücksetzen" : "Reset your password";

    console.log(
      "Forgot password mail template after updation",
      event.response.emailMessage
    );

    context.done(null, event);
  } else if (event.triggerSource === "CustomMessage_SignUp") {
    const { userAttributes } = event.request;
    const firstName = userAttributes["custom:firstName"] || "";
    const linkParameter = event.request.linkParameter;
    let emailHtmlContent = getWelcomeEmailTemplate(oapName, localization);
    emailHtmlContent = emailHtmlContent
      .replace("{{Recipient.FirstName}}", firstName)
      .replaceAll("{{Domain}}", domain)
      .replaceAll("{{LinkParameter}}", linkParameter)
      .replaceAll("{{Email}}", event.request.userAttributes.email)
      .replaceAll("{{UserName}}", event.userName)
      .replaceAll("{{ConfirmationCode}}", event.request.codeParameter);

    console.log("emailHtmlContent:", emailHtmlContent);

    event.response.emailMessage = emailHtmlContent;
    event.response.emailSubject = localization === "de" ? "UE Bewerbungsportal – Aktivierung Ihres Accounts," : "Your Email Verification link";
    context.done(null, event);
  }
};

export const preSignUp = async (event, context) => {
  console.log(JSON.stringify(event));
  const email: string = event.request.userAttributes.email; // Get email from event object
  const userPoolId: string = event.userPoolId; // Get User Pool ID from event object

  try {
    const user = await findUserByEmail(email, userPoolId);
    console.log("user ", user);

    if (user && user.UserStatus === "UNCONFIRMED") {
      const createdDate = new Date(user.UserCreateDate);
      const now = new Date();
      const hoursSinceCreation =
        (now.getTime() - createdDate.getTime()) / (1000 * 60 * 60);

      if (hoursSinceCreation >= DELETE_AFTER_HOURS) {
        // Delete the unconfirmed user
        await deleteUser(user.Username, userPoolId); // Use the found username to delete the user
        console.log(`Deleted unconfirmed user with email: ${email}`);
      }
    }

    context.done(null, event);
  } catch (error) {
    console.error("Error handling pre-signup:", error);
    throw error;
  }
};

const findUserByEmail = async (email: string, userPoolId: string) => {
  const params = {
    UserPoolId: userPoolId,
    Filter: `email = "${email}"`,
    Limit: 1,
  };

  try {
    const response = await cognito.listUsers(params).promise();
    return response.Users?.[0]; // Return the first user if found
  } catch (error) {
    console.error("Error finding user by email:", error);
    throw error;
  }
};

const deleteUser = async (username: string, userPoolId: string) => {
  const params = {
    UserPoolId: userPoolId,
    Username: username,
  };

  await cognito.adminDeleteUser(params).promise();
};

const getForgotPasswordEmailTemplate = (oapName: string, localization: string = "en") => {
  switch (oapName) {
    case "hzu":
      return hzuForgotPasswordEmailTemplate;
    case "lim":
      return limForgotPasswordEmailTemplate;
    case "ucw":
      return ucwForgotPasswordEmailTemplate;
    case "unfc":
      return unfcForgotPasswordEmailTemplate;
    case "ueg":
      return localization === "de" ? uegForgotPasswordEmailTemplateDe : uegForgotPasswordEmailTemplate;
    default:
      return uegForgotPasswordEmailTemplate;
  }
};

const getWelcomeEmailTemplate = (oapName: string, localization: string = "en") => {
  switch (oapName) {
    case "hzu":
      return hzuWelcomeEmailTemplate;
    case "lim":
      return limWelcomeEmailTemplate;
    case "ucw":
      return ucwWelcomeEmailTemplate;
    case "unfc":
      return unfcWelcomeEmailTemplate;
    case "ueg":
      return localization === "de" ? uegWelcomeEmailTemplateDe : uegWelcomeEmailTemplate;
    default:
      return uegWelcomeEmailTemplate;
  }
};

export const preAuthentication = async (event, context) => {
  console.log(JSON.stringify(event));
  const email = event.request.userAttributes.email;
  const userPoolId = event.userPoolId;
  const oapName = userPoolDomainMappings[userPoolId]?.oapName?.toUpperCase();
  
  if (!email || !oapName) {
    console.log("Missing email or brand information, proceeding with authentication");
    context.done(null, event);
    return;
  }

  try {
    // Check if user exists in the password change required table
    const params = {
      TableName: `gus-oap-student-details-${process.env.STAGE || 'dev'}`,
      Key: {
        PK: email,
        SK: oapName
      }
    };

    const result = await dynamoDB.get(params).promise();
    
    if (result.Item && result.Item.passwordChangeRequired === true) {
      console.log(`User ${email} requires password change for ${oapName}`);
      throw new Error("User must reset password before login.");
    }
    
    // If no item found or passwordChangeRequired is false, proceed with authentication
    console.log(`No password change required for ${email} in ${oapName}`);
    context.done(null, event);
  } catch (error) {
    console.error("Error in preAuthentication:", error);
    throw error;
  }
};
