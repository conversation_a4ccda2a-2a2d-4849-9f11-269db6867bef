import {
  hzuForgotPasswordEmailTemplate,
  hzuWelcomeEmailTemplate,
  limForgotPasswordEmailTemplate,
  limWelcomeEmailTemplate,
  ucwForgotPasswordEmailTemplate,
  ucwWelcomeEmailTemplate,
  unfcForgotPasswordEmailTemplate,
  unfcWelcomeEmailTemplate,
  uegWelcomeEmailTemplate,
  uegForgotPasswordEmailTemplate
} from "@functions/cognito-trigger/email-templates/index";
import { CognitoIdentityServiceProvider } from "aws-sdk";
import { userPoolDomainMappings } from "./userPoolDomainMappings";

const cognito = new CognitoIdentityServiceProvider();

const DELETE_AFTER_HOURS = 24; // Time after which to delete unconfirmed users

export const studentOAPCustomEmail = async (event, context) => {
  console.log(JSON.stringify(event));

  const domain = userPoolDomainMappings[event.userPoolId]?.domain;
  const oapName = userPoolDomainMappings[event.userPoolId]?.oapName;

  if (event.triggerSource === "CustomMessage_ForgotPassword") {
    const { userAttributes } = event.request;
    const firstName = userAttributes["custom:firstName"] || "";
    const resetLink = `${domain}/change-password?confirmation_code=${event.request.codeParameter}&user_name=${event.userName}`;
    let emailHtmlContent = getForgotPasswordEmailTemplate(oapName);

    emailHtmlContent = emailHtmlContent
      .replace("{{Recipient.FirstName}}", firstName)
      .replaceAll("{{ResetLink}}", resetLink);
    event.response.emailMessage = emailHtmlContent;
    event.response.emailSubject = "Reset your password";
    
    console.log(
      "Forgot password mail template after updation",
      event.response.emailMessage
    );

    context.done(null, event);
  } else if (event.triggerSource === "CustomMessage_SignUp") {
    const { userAttributes } = event.request;
    const firstName = userAttributes["custom:firstName"] || "";
    const linkParameter = event.request.linkParameter;
    let emailHtmlContent = getWelcomeEmailTemplate(oapName);
    emailHtmlContent = emailHtmlContent
      .replace("{{Recipient.FirstName}}", firstName)
      .replaceAll("{{Domain}}", domain)
      .replaceAll("{{LinkParameter}}", linkParameter)
      .replaceAll("{{Email}}", event.request.userAttributes.email)
      .replaceAll("{{UserName}}", event.userName)
      .replaceAll("{{ConfirmationCode}}", event.request.codeParameter);

    console.log("emailHtmlContent:", emailHtmlContent);

    event.response.emailMessage = emailHtmlContent;
    event.response.emailSubject = "Your Email Verification link";
    context.done(null, event);
  }
};

export const preSignUp = async (event, context) => {
  console.log(JSON.stringify(event));
  const email: string = event.request.userAttributes.email; // Get email from event object
  const userPoolId: string = event.userPoolId; // Get User Pool ID from event object

  try {
    const user = await findUserByEmail(email, userPoolId);
    console.log("user ", user);

    if (user && user.UserStatus === "UNCONFIRMED") {
      const createdDate = new Date(user.UserCreateDate);
      const now = new Date();
      const hoursSinceCreation =
        (now.getTime() - createdDate.getTime()) / (1000 * 60 * 60);

      if (hoursSinceCreation >= DELETE_AFTER_HOURS) {
        // Delete the unconfirmed user
        await deleteUser(user.Username, userPoolId); // Use the found username to delete the user
        console.log(`Deleted unconfirmed user with email: ${email}`);
      }
    }

    context.done(null, event);
  } catch (error) {
    console.error("Error handling pre-signup:", error);
    throw error;
  }
};

const findUserByEmail = async (email: string, userPoolId: string) => {
  const params = {
    UserPoolId: userPoolId,
    Filter: `email = "${email}"`,
    Limit: 1,
  };

  try {
    const response = await cognito.listUsers(params).promise();
    return response.Users?.[0]; // Return the first user if found
  } catch (error) {
    console.error("Error finding user by email:", error);
    throw error;
  }
};

const deleteUser = async (username: string, userPoolId: string) => {
  const params = {
    UserPoolId: userPoolId,
    Username: username,
  };

  await cognito.adminDeleteUser(params).promise();
};

const getForgotPasswordEmailTemplate = (oapName: string) => {
  switch (oapName) {
    case "hzu":
      return hzuForgotPasswordEmailTemplate;
    case "lim":
      return limForgotPasswordEmailTemplate;
    case "ucw":
      return ucwForgotPasswordEmailTemplate;
    case "unfc":
      return unfcForgotPasswordEmailTemplate;
    case "ueg":
      return uegForgotPasswordEmailTemplate;
  }
};

const getWelcomeEmailTemplate = (oapName: string) => {
  switch (oapName) {
    case "hzu":
      return hzuWelcomeEmailTemplate;
    case "lim":
      return limWelcomeEmailTemplate;
    case "ucw":
      return ucwWelcomeEmailTemplate;
    case "unfc":
      return unfcWelcomeEmailTemplate;
    case "ueg":
      return uegWelcomeEmailTemplate;
  }
};
