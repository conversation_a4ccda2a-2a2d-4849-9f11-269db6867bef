import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
  salesforceStudentConfig,
  salesforceStudentSubObjectsConfig,
} from "src/functions/hzu-service/salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });

const sqs = new AWS.SQS();

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();

export const handleHzuSfSaveOrUpdateRequests = async (event) => {
  console.log("event -->", JSON.stringify(event));
  const brand = "HZU";

  // Ensure event is iterable
  if (!event || !Array.isArray(event)) {
    throw new Error("Event must be an array of records");
  }

  for (let record of event) {
    let applicationDetails = JSON.parse(record["body"]);
    applicationDetails = convertYesNoToBoolean(applicationDetails);
    const requestId = applicationDetails.requestId;
    const filledBy = applicationDetails?.applicationFilledBy;
    const APIKEY = applicationDetails.APIKEY;
    if (applicationDetails?.privacyPolicyConsent) {
      applicationDetails.privacyPolicyConsent =
        !applicationDetails?.privacyPolicyConsent;
    }
    const currentUTC = new Date().toISOString();
    let referenceMappedDetails;
    try {
      if (
        applicationDetails?.applicationStatus === "submitted" &&
        !applicationDetails?.submittedToSf
      ) {
        applicationDetails.identityDocumentObject = [
          {
            identityNumber: applicationDetails.passportNumber,
            identityIssueDate: applicationDetails.passportIssueDate,
            identityExpiryDate: applicationDetails.passportExpiryDate,
            identityIssuingCountry:
              applicationDetails.passportIssuingCountryDisplayName,
            identityType: "Passport",
          },
        ];
        // Safely iterate through priorInstitutionsAttended if it exists and is an array
        if (Array.isArray(applicationDetails.priorInstitutionsAttended)) {
          for (let cert of applicationDetails.priorInstitutionsAttended) {
            if (cert && Array.isArray(cert.institutionCertificates)) {
              for (let certId of cert.institutionCertificates) {
                if (Array.isArray(applicationDetails.documents)) {
                  for (let doc of applicationDetails.documents) {
                    if (certId === doc.documentId) {
                      doc.institutionName = cert.institutionName;
                    }
                  }
                }
              }
            }
          }
        }

        console.log(
          "Modified Application details document -->",
          applicationDetails.documents
        );
        const mappings = {
          priorInstitutionsAttended: "educationHistory",
          testInfo: "languageProficiency",
          recommenders: "connections",
          employmentHistory: "workHistory",
        };
        for (const [sourceKey, targetKey] of Object.entries(mappings)) {
          if (applicationDetails.hasOwnProperty(sourceKey)) {
            applicationDetails[targetKey] = applicationDetails[sourceKey];
          }
        }
        referenceMappedDetails = await opportunityFileReferenceMapping(
          applicationDetails
        );

        applicationDetails = referenceMappedDetails.appDetails;
        applicationDetails = {
          ...applicationDetails,
          ...buildMappings(applicationDetails),
        };
      }
      let request = {};
      request["sectionLabel"] = applicationDetails.sectionLabel;
      request["email"] = applicationDetails.email;
      request["applicationId"] = applicationDetails.applicationId;
      request["requestId"] = requestId;
      request["messageId"] = record.messageId;
      request = {
        ...request,
        ...(await mapSalesforceObject(applicationDetails, filledBy)),
      };
      console.log("request ->", request);
      await postDataSf(request, APIKEY, filledBy);
      await sqs.deleteMessage({
        QueueUrl: process.env.LIM_EIP_OUEUE_URL,
        ReceiptHandle: record.receiptHandle,
      });

      await cloudWatchLoggerService.log(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        "Successfully saved to salesforce",
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    } catch (error) {
      console.error("Error in handleHzuSfSaveOrUpdateRequests:", error);

      // Create a meaningful error message
      const errorMessage = error.message || 'Unknown error occurred';
      const errorStack = error.stack || 'No stack trace available';
      const errorDetails = {
        message: errorMessage,
        stack: errorStack,
        requestId: requestId,
        applicationId: applicationDetails?.applicationId,
        email: applicationDetails?.email
      };

      await cloudWatchLoggerService.error(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SAVED_TO_SALESFORCE,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        JSON.stringify(errorDetails),
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );

      // Throw a meaningful error instead of empty object
      throw new Error(`HZU Service Error: ${errorMessage}. RequestId: ${requestId}`);
    }
  }
};

function opportunityFileReferenceMapping(appDetails) {
  if (appDetails && Array.isArray(appDetails.documents)) {
    const documentMap = new Map();
    appDetails.documents.forEach((doc) => {
      documentMap.set(doc.documentId, doc);
    });

    const addReferences = (documentIds, referenceKey, referenceValue) => {
      if (Array.isArray(documentIds)) {
        documentIds.forEach((docId) => {
          const matchingDocument = documentMap.get(docId);
          if (matchingDocument) {
            matchingDocument[referenceKey] = referenceValue;
          }
        });
      }
    };

    if (Array.isArray(appDetails.educationHistory)) {
      appDetails.educationHistory.forEach((institution, index) => {
        const institutionOrder = index + 1;
        const institutionCertificates =
          institution.institutionCertificates || [];

        const documentIds = [
          ...institutionCertificates.map((doc) => doc.documentId),
        ];

        addReferences(
          documentIds,
          "eduReference",
          `@{EducationHistoryRecord__c_${institutionOrder}.id}`
        );
      });
    } else {
      console.error("No institutions found in appDetails. Nothing to process.");
    }

    const categories = [
      {
        key: "passportDocument",
        referenceKey: "identityReference",
        referenceValue: "@{IdentityInfoRecord__c_1.id}",
      },
      {
        key: "languageProficiencyDocuments",
        referenceKey: "testReference",
        referenceValue: "@{LanguageProficiencyRecord__c_1.id}",
      },
    ];

    if (Array.isArray(categories)) {
      categories.forEach(({ key, referenceKey, referenceValue }) => {
        if (Array.isArray(appDetails[key])) {
          const documentIds = appDetails[key].map((doc) => doc.documentId);
          addReferences(documentIds, referenceKey, referenceValue);
        }
      });
    }

    console.log("Processed appDetails.documents:", appDetails.documents);

    return { appDetails };
  } else {
    console.error("Invalid or missing appDetails.documents.");
  }

  return { appDetails };
}

function buildMappings(applicationDetails) {
  try {
    const {
      emergencyContactFirstName,
      emergencyContactLastName,
      emergencyContactRelationship,
      emergencyContactPhoneNumber,
      emergencyContactEmail,
      testNameDisplayName,
      testReportFormNo,
      testDate,
      testVerbal,
      testVerbalPercentile,
      testAnalyticalWriting,
      testAnalyticalWritingPercentile,
      employerName,
      employerPosition,
      employmentStartDate,
      employmentWorkPhone,
    } = applicationDetails;

    return {
      connections: [
        {
          emergencyContactFirstName,
          emergencyContactLastName,
          emergencyContactRelationship,
          emergencyContactPhoneNumber,
          emergencyContactEmail,
          connectionType: "Emergency",
        },
      ],
      languageProficiency: [
        {
          testNameDisplayName,
          testReportFormNo,
          testDate,
          testVerbal,
          testVerbalPercentile,
          testAnalyticalWriting,
          testAnalyticalWritingPercentile,
        },
      ],
      workHistory: [
        {
          employerName,
          employerPosition,
          employmentStartDate,
          employmentWorkPhone,
        },
      ],
      miscDetails: JSON.stringify({
        Citizenship_Status__c: applicationDetails?.citizenshipStatus,
        Ethinicity__c: applicationDetails?.ethinicity,
        Race__c: applicationDetails?.race,
        Preferred_Contact_Time__c: applicationDetails?.preferredContactTime,
        Highschool_Completion_Status__c:
          applicationDetails?.highSchoolCompletionStatusDisplayName,
      }),
    };
  } catch (error) {
    console.error("Error in opportunityFileReferenceMapping:", error);
    throw new Error(`opportunityFileReferenceMapping failed: ${error.message || error}`);
  }
}

function convertYesNoToBoolean(data) {
  try {
    const keys = [
      "isCurrentEmployee",
      "isEmployerOfferTuition",
      "isFirstGenerationCollegeStudent",
      "haveWorkExperience",
      "priorInstitutionsAttended.canShareTransferringCredits",
      "haveTrafficViolation",
    ];
    for (const key of keys) {
      if (key.includes(".")) {
        const [parentKey, childKey] = key.split(".");
        console.log(parentKey, childKey);
        if (data[parentKey] && Array.isArray(data[parentKey])) {
          for (const item of data[parentKey]) {
            if (item && typeof item === "object") {
              item[childKey] = item[childKey] === "Yes";
            }
          }
        }
      } else if (
        typeof data[key] === "string" &&
        (data[key] === "Yes" || data[key] === "No")
      ) {
        data[key] = data[key] === "Yes";
      }
    }
    return data;
  } catch (error) {
    console.error("Error in convertYesNoToBoolean:", error);
    throw new Error(`convertYesNoToBoolean failed: ${error.message || error}`);
  }
}

async function mapSalesforceObject(applicationDetails, filledBy): Promise<any> {
  try {
    let salesforceConfig =
      filledBy === "agent"
        ? { ...salesforceAgentConfig }
        : { ...salesforceStudentConfig };
    if (applicationDetails.hasOwnProperty("notMetEligibilityCreteria")) {
      if (applicationDetails.notMetEligibilityCreteria === true) {
        const keys = Object.keys(salesforceConfig);
        for (const key of keys) {
          if (!["Survey_AP__c"].includes(key)) {
            delete salesforceConfig[key];
          }
        }
      }
      console.log("Survey Object Created Successfully", salesforceConfig);

      applicationDetails.surveyIsQualified =
        !applicationDetails.notMetEligibilityCreteria;
    } else {
      delete salesforceConfig["Survey_AP__c"];
    }
    const salesforceSubObjectsConfig =
      filledBy === "agent"
        ? salesforceAgentSubObjectsConfig
        : salesforceStudentSubObjectsConfig;
    let result = {};
    for (const object in salesforceConfig) {
      if (Array.isArray(salesforceConfig[object])) {
        const arrayKey = salesforceConfig[object][0];
        if (applicationDetails[arrayKey] && Array.isArray(applicationDetails[arrayKey])) {
          for (const record of applicationDetails[arrayKey]) {
            let mappedObject = await mapValues(
              record,
              salesforceSubObjectsConfig[salesforceConfig[object][0]]
            );
            if (mappedObject && Object.keys(mappedObject).length > 0) {
              if (!result[object]) {
                result[object] = [];
              }
              result[object].push(mappedObject);
            }
          }
        }
      } else {
        let mappedObject = await mapValues(
          applicationDetails,
          salesforceConfig[object]
        );
        console.log(mappedObject);
        if (mappedObject && Object.keys(mappedObject).length > 0) {
          result[object] = mappedObject;
        }
      }
    }
    return result;
  } catch (error) {
    console.error("Error in mapSalesforceObject:", error);
    throw new Error(`mapSalesforceObject failed: ${error.message || error}`);
  }
}

async function mapValues(source, mapping) {
  const result = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value;

    if (sourceKey.includes(",")) {
      const keys = sourceKey.split(",");
      const values = [];

      for (const key of keys) {
        if (key.includes(".")) {
          const nestedKeys = key.trim().split(".");
          let nestedValue = source;

          for (const nestedKey of nestedKeys) {
            nestedValue = nestedValue ? nestedValue[nestedKey] : undefined;
          }
          if (nestedValue !== undefined) {
            values.push(nestedValue);
          }
        } else {
          const simpleValue = source[key.trim()];
          if (simpleValue !== undefined) {
            values.push(simpleValue);
          }
        }
      }

      value = values.join(" ");
    } else if (sourceKey.includes(".")) {
      const keys = sourceKey.split(".");
      value = source;

      for (const key of keys) {
        value = value ? value[key] : undefined;
      }
    } else {
      value = source[sourceKey];
    }

    if (value !== undefined) {
      if (Array.isArray(targetKey)) {
        targetKey.forEach((key) => {
          result[key] = value;
        });
      } else {
        result[targetKey] = value;
      }
    }
  }

  return result;
}
