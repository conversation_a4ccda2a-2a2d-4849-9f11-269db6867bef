import { handlerPath } from '@libs/handler-resolver';

export const hzuSfQueue = {
    handler: `${handlerPath(__dirname)}/hzuHandler.handleHzuSfRequests`,
    name: 'hzu-sf-sqs-${self:provider.stage}',
    events: [
        {
            sqs: '${self:provider.environment.HZU_EIP_QUEUE}',
        }
    ],
    timeout: 180
};

export const hzuChangeRequestQueue = {
    handler: `${handlerPath(__dirname)}/hzuChangeRequestHandler.handleHzuChangeRequests`,
    name: 'hzu-change-request-sqs-${self:provider.stage}',
    // events: [
    //     {
    //         sqs: {
    //             arn: 'arn:aws:sqs:eu-west-1:${aws:accountId}:${self:custom.variables.stagePrefix.${self:provider.stage}}-HZU-EIP-CR-QUEUE.fifo'
    //         }
    //     }
    // ],
    timeout: 180
};
